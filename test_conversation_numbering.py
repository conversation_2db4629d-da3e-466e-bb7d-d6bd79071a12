#!/usr/bin/env python3
"""
Test script for conversation numbering implementation.
This script tests that conversations are properly tracked with numbers,
resets work correctly, and conversation history is preserved for analytics.
"""

import os
import sys
import logging
from datetime import datetime, timezone

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.conversational_engine import ConversationalEngine
from core.supabase_client import supabase_client
from core.models import generate_terminal_chat_id

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_conversation_numbering():
    """Test the conversation numbering implementation."""
    
    # Use a test bot ID (Metro Farm Digital Twin)
    bot_id = "fb194cb4-2c57-42bf-b080-c9389065be02"
    test_user_id = "test_user_conversation_numbering"
    chat_id = generate_terminal_chat_id(bot_id, test_user_id)
    
    logger.info(f"Testing conversation numbering with chat_id: {chat_id}")
    
    try:
        # Initialize conversational engine
        engine = ConversationalEngine(bot_id)
        
        # Test 1: Start first conversation
        logger.info("=== Test 1: First Conversation ===")
        response1 = engine.generate_response(
            bot_id=bot_id,
            user_message="Hello, tell me about your farm.",
            chat_id=chat_id
        )
        logger.info(f"Response 1: {response1.response[:100]}...")
        
        # Check current conversation number
        current_number = supabase_client.get_current_conversation_number(chat_id)
        logger.info(f"Current conversation number after first message: {current_number}")
        assert current_number == 1, f"Expected conversation number 1, got {current_number}"
        
        # Test 2: Add more messages to first conversation
        logger.info("=== Test 2: Continue First Conversation ===")
        response2 = engine.generate_response(
            bot_id=bot_id,
            user_message="What crops do you grow?",
            chat_id=chat_id
        )
        logger.info(f"Response 2: {response2.response[:100]}...")
        
        # Verify still in conversation 1
        current_number = supabase_client.get_current_conversation_number(chat_id)
        logger.info(f"Current conversation number after second message: {current_number}")
        assert current_number == 1, f"Expected conversation number 1, got {current_number}"
        
        # Test 3: Reset conversation
        logger.info("=== Test 3: Reset Conversation ===")
        reset_success = engine.reset_conversation(bot_id=bot_id, chat_id=chat_id)
        logger.info(f"Reset successful: {reset_success}")
        assert reset_success, "Conversation reset should succeed"
        
        # Test 4: Start second conversation
        logger.info("=== Test 4: Second Conversation ===")
        response3 = engine.generate_response(
            bot_id=bot_id,
            user_message="Hi again, what's your farming philosophy?",
            chat_id=chat_id
        )
        logger.info(f"Response 3: {response3.response[:100]}...")
        
        # Check that we're now in conversation 2
        current_number = supabase_client.get_current_conversation_number(chat_id)
        logger.info(f"Current conversation number after reset: {current_number}")
        assert current_number == 2, f"Expected conversation number 2, got {current_number}"
        
        # Test 5: Verify conversation history is preserved
        logger.info("=== Test 5: Verify History Preservation ===")
        
        # Get conversation 1 history
        history_1 = supabase_client.get_conversation_history(chat_id, conversation_number=1)
        logger.info(f"Conversation 1 has {len(history_1)} messages")
        assert len(history_1) >= 4, f"Expected at least 4 messages in conversation 1, got {len(history_1)}"
        
        # Get conversation 2 history
        history_2 = supabase_client.get_conversation_history(chat_id, conversation_number=2)
        logger.info(f"Conversation 2 has {len(history_2)} messages")
        assert len(history_2) >= 2, f"Expected at least 2 messages in conversation 2, got {len(history_2)}"
        
        # Verify conversation numbers in messages
        for msg in history_1:
            assert msg.conversation_number == 1, f"Expected conversation_number 1, got {msg.conversation_number}"
        
        for msg in history_2:
            assert msg.conversation_number == 2, f"Expected conversation_number 2, got {msg.conversation_number}"
        
        # Test 6: Verify conversation states are preserved
        logger.info("=== Test 6: Verify State Preservation ===")
        
        # Get conversation 1 state
        state_1 = supabase_client.get_conversation_state(chat_id, conversation_number=1)
        assert state_1 is not None, "Conversation 1 state should exist"
        assert state_1.conversation_number == 1, f"Expected conversation_number 1, got {state_1.conversation_number}"
        logger.info(f"Conversation 1 state: summary length = {len(state_1.summary)}")
        
        # Get conversation 2 state
        state_2 = supabase_client.get_conversation_state(chat_id, conversation_number=2)
        assert state_2 is not None, "Conversation 2 state should exist"
        assert state_2.conversation_number == 2, f"Expected conversation_number 2, got {state_2.conversation_number}"
        logger.info(f"Conversation 2 state: summary length = {len(state_2.summary)}")
        
        logger.info("=== All Tests Passed! ===")
        logger.info("✅ Conversation numbering is working correctly")
        logger.info("✅ Conversation history is preserved for analytics")
        logger.info("✅ Conversation resets increment conversation number")
        logger.info("✅ Each conversation maintains separate state")
        
        return True
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_conversation_numbering()
    if success:
        print("\n🎉 All conversation numbering tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Conversation numbering tests failed!")
        sys.exit(1)
